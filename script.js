document.addEventListener('DOMContentLoaded', function() {
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');

    togglePassword.addEventListener('click', function() {
        // 切换输入框的类型
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        // 可选：切换眼睛图标的样式（开/闭）
        // 这里只是简单地改变颜色，也可以替换SVG
        this.style.color = type === 'password' ? '#aaa' : '#4db6ac';
    });
});