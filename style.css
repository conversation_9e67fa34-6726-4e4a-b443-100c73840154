/* 全局样式和字体设置 */
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: #333;
}

/* 登录容器 */
.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    box-sizing: border-box; /* 确保内边距不会增加总宽度 */
}

/* 头部标题 */
.header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 40px;
    color: #000;
}

/* 表单组 */
.form-group {
    margin-bottom: 25px;
}

/* 标签 */
.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #555;
}

/* 通用输入框样式 */
input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    box-sizing: border-box; /* 确保内边距不会增加总宽度 */
}

input[type="text"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #4db6ac; /* 登录按钮的颜色 */
}

/* 密码框和眼睛图标的包裹容器 */
.password-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-wrapper input {
    padding-right: 50px; /* 为图标留出空间 */
}

/* 切换密码可见性的图标 */
.toggle-password {
    position: absolute;
    right: 15px;
    cursor: pointer;
    color: #aaa;
}

/* "记住密码" 复选框 */
.remember-me {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.remember-me input[type="checkbox"] {
    margin-right: 10px;
    /* 样式化复选框 */
    width: 20px;
    height: 20px;
    accent-color: #4db6ac;
}

/* 登录按钮 */
.btn-login {
    width: 100%;
    padding: 15px;
    background-color: #4db6ac; /* 图像中的青蓝色 */
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-login:hover {
    background-color: #26a69a; /* 鼠标悬停时颜色变深 */
}